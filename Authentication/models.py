from django.db import models

from cryptography.fernet import Fernet
from core.settings import ENCRYP<PERSON>ON_KEY

import random
import string


class UserRegistration(models.Model):
    name = models.CharField(max_length=250)
    dob = models.CharField(max_length=250,blank=True,null=True,default='')
    email = models.CharField(max_length=250)
    bio = models.TextField(null=True,blank=True,default='')
    gender = models.CharField(max_length=250,null=True,blank=True,default='')
    enc_email = models.Char<PERSON>ield(max_length=250)
    username = models.CharField(max_length=250,null=True,blank=True)
    temp_password = models.CharField(max_length=250,null=True,blank=True)
    password = models.Char<PERSON>ield(max_length=250)
    phone = models.CharField(max_length=250)
    current_status = models.Char<PERSON>ield(max_length=250,default='2')
    user_type = models.Char<PERSON>ield(max_length=250,default='User')
    user_industry = models.Char<PERSON><PERSON>(max_length=250,default='None')
    verify_phone = models.CharField(max_length=250)
    report_count = models.IntegerField(default=0)
    is_banned = models.BooleanField(default=False)
    is_deleted = models.BooleanField(default=False)
    otp = models.CharField(max_length=250,blank=True,null=True,default='')
    profile_picture = models.FileField(upload_to='media-profile/',blank=True,null=True)
    profile_url = models.CharField(max_length=255,blank=True,null=True,default='')
    is_private = models.BooleanField(default=False)
    is_subsribed = models.BooleanField(null=True,blank=True,default=False)
    is_admin = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True)
    onesignal_player =  models.CharField(default='',blank=True,null=True)
    socket_id = models.CharField(max_length=250,default='')
    country = models.CharField(max_length=550,default='')
    state = models.CharField(max_length=550,default='')
    city = models.CharField(max_length=550,default='')
    refference_code = models.CharField(max_length=550,default='', blank=True, null=True)

    def generate_unique_refference_code(self, length=18):
        chars = string.ascii_uppercase + string.digits
        while True:
            code = ''.join(random.choices(chars, k=length))
            if not UserRegistration.objects.filter(refference_code=code).exists():
                return code

    def save(self, *args, **kwargs):
        if self.pk is None and not self.refference_code:
            self.refference_code = self.generate_unique_refference_code()
        super(UserRegistration, self).save(*args, **kwargs)


class Reffrence(models.Model):
    invitee = models.ForeignKey(UserRegistration,related_name='invitee_user_refference',on_delete=models.CASCADE)
    invited = models.ForeignKey(UserRegistration,related_name='invited_user_refference',on_delete=models.CASCADE)
    is_point_given = models.BooleanField(default=False)
    is_first_activity = models.BooleanField(default=False)
        
class Brands(models.Model):
    user = models.ForeignKey(UserRegistration,on_delete=models.CASCADE)
    name = models.CharField(max_length=250)
    email = models.CharField(max_length=250)
    domain = models.CharField(max_length=250,default='')
    logo = models.FileField(upload_to='media-profile/',blank=True,null=True)
    user_list = models.JSONField(default=list)
    
class ThirdPartyAuth(models.Model):
    brand = models.ForeignKey(Brands,on_delete=models.CASCADE,blank=True,null=True)
    facebook_check = models.BooleanField(default=False)
    facebook_token = models.CharField(max_length=1500)
    facebook_page_id = models.CharField(max_length=1500)
    instagram_check = models.BooleanField(default=False)
    insta_user_id = models.CharField(max_length=250,default='')
    insta_auth_token = models.CharField(max_length=250,default='')
    thread_check = models.BooleanField(default=False)
    thread_user_id = models.CharField(max_length=250,default='')
    thread_auth_token = models.CharField(max_length=250,default='')
    facebook_creds = models.CharField(max_length=150,default='')
    linkedin_check = models.BooleanField(default=False)
    linkedin_creds = models.CharField(max_length=500,default='')
    linked_in_token = models.CharField(max_length=500,default='')
    pinterest_check = models.BooleanField(default=False)
    pinterest_creds = models.CharField(max_length=150,default='')
    pinterest_username = models.CharField(max_length=150,default='')
    vimeo_check = models.BooleanField(default=False)
    vimeo_creds = models.CharField(max_length=150,default='')
    vimeo_username = models.CharField(max_length=150,default='')
    tumblr_check = models.BooleanField(default=False)
    tumbler_token = models.CharField(max_length=150,default='')
    tumbler_secret = models.CharField(max_length=150,default='')
    tumbler_blogname = models.CharField(max_length=150,default='')
    youtube_refresh_token = models.CharField(max_length=500,default='')
    youtube_check = models.BooleanField(default=False)
    reddit_check = models.BooleanField(default=False)
    reddit_username = models.CharField(max_length=1500,default='')
    reddit_token = models.CharField(max_length=1500,default='')
    tiktok_check = models.BooleanField(default=False)
    tiktok_access_token = models.CharField(max_length=1500,default='')
    tiktok_refresh_token = models.CharField(max_length=1500,default='')
    x_refresh_token = models.CharField(max_length=1500,default='')
    x_check = models.BooleanField(default=False)
    x_access_token = models.CharField(max_length=1500,default='')
    x_user_id = models.CharField(max_length=1500,default='')
    # line_check = models.BooleanField(default=False)
    # line_access_token = models.CharField(max_length=1500,default='')
    # line_refresh_token = models.CharField(max_length=1500,default='')
    # line_user_id = models.CharField(max_length=1500,default='')
    # line_channel_id = models.CharField(max_length=1500,default='')


class Report(models.Model):
    reporting_user = models.ForeignKey(UserRegistration,on_delete=models.CASCADE,related_name='reporting_user')
    reported_user = models.ForeignKey(UserRegistration,on_delete=models.CASCADE,related_name='reported_user')
    reason = models.TextField(default='') 
    created_at = models.DateTimeField(auto_now_add=True)

class Block(models.Model):
    from_user = models.ForeignKey(UserRegistration, related_name='blocker', on_delete=models.CASCADE)
    to_user = models.ForeignKey(UserRegistration, related_name='blocked', on_delete=models.CASCADE)
    is_block = models.BooleanField(default=False)

class SurveyForm(models.Model):
    user = models.ForeignKey(UserRegistration, on_delete=models.CASCADE)
    name = models.CharField(max_length=250)
    is_app_used = models.BooleanField(default=False)
    app_name = models.CharField(max_length=255,blank=True,null=True,default='')
    app_description = models.CharField(max_length=1500,blank=True,null=True,default='')
    is_opt_in = models.BooleanField(default=False)

class Subscriptions(models.Model):
    subscription_name = models.CharField(max_length=250)
    subscription_price = models.CharField(max_length=250)
    subscription_duration = models.CharField(max_length=250)
    subscription_description = models.JSONField(default=dict)
    is_live_status = models.BooleanField(default=True)

class UserSubscription(models.Model):
    user = models.ForeignKey(UserRegistration, on_delete=models.CASCADE)
    subscription = models.ForeignKey(Subscriptions, on_delete=models.CASCADE)
    start_date = models.CharField(max_length=250,default='')
    end_date = models.CharField(max_length=250,default='')
    is_active = models.BooleanField(default=True)

class UserManagement(models.Model):
    user = models.ForeignKey(UserRegistration, on_delete=models.CASCADE,related_name='admin_user')
    user_invited = models.ForeignKey(UserRegistration, on_delete=models.CASCADE,related_name='invited_user')
    brand_id = models.JSONField(default=list)
    permission = models.CharField(max_length=250,default=1)
    created_at = models.DateTimeField(auto_now_add=True)
    is_accepted = models.BooleanField(default=False)

class UserBrandAccessManagement(models.Model):
    user_management = models.ForeignKey(UserManagement, on_delete=models.CASCADE)
    brand = models.ForeignKey(Brands, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)


class UserRoles(models.Model):
    user = models.ForeignKey(UserRegistration, on_delete=models.CASCADE)
    role_name = models.CharField(max_length=250)
    role_description = models.JSONField(default=dict)
    created_at = models.DateTimeField(auto_now_add=True)

class Industry(models.Model):
    name = models.CharField(max_length=250)
    created_at = models.DateTimeField(auto_now_add=True)

class UserTypes(models.Model):
    name = models.CharField(max_length=250)
    created_at = models.DateTimeField(auto_now_add=True)

class CurrentBrand(models.Model):
    user = models.ForeignKey(UserRegistration, on_delete=models.CASCADE)
    brand = models.ForeignKey(Brands, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)

class BetaTester(models.Model):
    name = models.CharField(max_length=550)
    email = models.CharField(max_length=550)
    gender = models.CharField(max_length=550)
    age = models.CharField(max_length=550)
    mobile = models.CharField(max_length=550)
    platform_status = models.CharField(max_length=550)
    description = models.CharField(max_length=5500,default='')

class Country(models.Model):
    country_id = models.CharField(max_length=250)
    name = models.CharField(max_length=550)

class State(models.Model):
    state_id = models.CharField(max_length=250)
    name = models.CharField(max_length=550)
    country_id = models.CharField(max_length=250)

class City(models.Model):
    city_id = models.CharField(max_length=250)
    name = models.CharField(max_length=550)
    state_id = models.CharField(max_length=250)

class Version(models.Model):
    version_number = models.CharField(max_length=550)
    
    def __str__(self):
        return f'{self.version_number}'
    
class BetaVersion(models.Model):
    version_number = models.CharField(max_length=550)
    
    def __str__(self):
        return f'{self.version_number}'

class SpecialSocial(models.Model):
    user = models.ForeignKey(UserRegistration, on_delete=models.CASCADE)
    is_active = models.BooleanField(default=False)
    created_at = models.DateTimeField(auto_now_add=True,null=True,blank=True)
