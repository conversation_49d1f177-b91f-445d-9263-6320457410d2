import requests
import json
from datetime import datetime

# LINE Messaging API URLs
LINE_MESSAGING_API_BASE = "https://api.line.me/v2/bot"
LINE_PUSH_MESSAGE_URL = f"{LINE_MESSAGING_API_BASE}/message/push"
LINE_REPLY_MESSAGE_URL = f"{LINE_MESSAGING_API_BASE}/message/reply"
LINE_MULTICAST_MESSAGE_URL = f"{LINE_MESSAGING_API_BASE}/message/multicast"
LINE_BROADCAST_MESSAGE_URL = f"{LINE_MESSAGING_API_BASE}/message/broadcast"
LINE_PROFILE_URL = f"{LINE_MESSAGING_API_BASE}/profile"
LINE_GROUP_SUMMARY_URL = f"{LINE_MESSAGING_API_BASE}/group"
LINE_ROOM_SUMMARY_URL = f"{LINE_MESSAGING_API_BASE}/room"

def send_line_push_message(access_token, to_user_id, messages):
    """
    Send push message to LINE user
    
    Args:
        access_token (str): LINE channel access token
        to_user_id (str): Target user ID
        messages (list): List of message objects
    
    Returns:
        dict: API response
    """
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {access_token}"
    }
    
    payload = {
        "to": to_user_id,
        "messages": messages
    }
    
    response = requests.post(LINE_PUSH_MESSAGE_URL, headers=headers, json=payload)
    
    if response.status_code == 200:
        return {"success": True, "data": response.json()}
    else:
        return {"success": False, "error": response.text, "status_code": response.status_code}

def send_line_reply_message(access_token, reply_token, messages):
    """
    Send reply message to LINE user
    
    Args:
        access_token (str): LINE channel access token
        reply_token (str): Reply token from webhook event
        messages (list): List of message objects
    
    Returns:
        dict: API response
    """
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {access_token}"
    }
    
    payload = {
        "replyToken": reply_token,
        "messages": messages
    }
    
    response = requests.post(LINE_REPLY_MESSAGE_URL, headers=headers, json=payload)
    
    if response.status_code == 200:
        return {"success": True, "data": response.json()}
    else:
        return {"success": False, "error": response.text, "status_code": response.status_code}

def send_line_multicast_message(access_token, to_user_ids, messages):
    """
    Send multicast message to multiple LINE users
    
    Args:
        access_token (str): LINE channel access token
        to_user_ids (list): List of target user IDs
        messages (list): List of message objects
    
    Returns:
        dict: API response
    """
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {access_token}"
    }
    
    payload = {
        "to": to_user_ids,
        "messages": messages
    }
    
    response = requests.post(LINE_MULTICAST_MESSAGE_URL, headers=headers, json=payload)
    
    if response.status_code == 200:
        return {"success": True, "data": response.json()}
    else:
        return {"success": False, "error": response.text, "status_code": response.status_code}

def send_line_broadcast_message(access_token, messages):
    """
    Send broadcast message to all LINE followers
    
    Args:
        access_token (str): LINE channel access token
        messages (list): List of message objects
    
    Returns:
        dict: API response
    """
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {access_token}"
    }
    
    payload = {
        "messages": messages
    }
    
    response = requests.post(LINE_BROADCAST_MESSAGE_URL, headers=headers, json=payload)
    
    if response.status_code == 200:
        return {"success": True, "data": response.json()}
    else:
        return {"success": False, "error": response.text, "status_code": response.status_code}

def get_line_user_profile(access_token, user_id):
    """
    Get LINE user profile
    
    Args:
        access_token (str): LINE channel access token
        user_id (str): Target user ID
    
    Returns:
        dict: User profile data
    """
    headers = {
        "Authorization": f"Bearer {access_token}"
    }
    
    url = f"{LINE_PROFILE_URL}/{user_id}"
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:
        return {"success": True, "data": response.json()}
    else:
        return {"success": False, "error": response.text, "status_code": response.status_code}

def get_line_group_summary(access_token, group_id):
    """
    Get LINE group chat summary
    
    Args:
        access_token (str): LINE channel access token
        group_id (str): Group ID
    
    Returns:
        dict: Group summary data
    """
    headers = {
        "Authorization": f"Bearer {access_token}"
    }
    
    url = f"{LINE_GROUP_SUMMARY_URL}/{group_id}/summary"
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:
        return {"success": True, "data": response.json()}
    else:
        return {"success": False, "error": response.text, "status_code": response.status_code}

def create_text_message(text):
    """
    Create a text message object for LINE
    
    Args:
        text (str): Message text
    
    Returns:
        dict: Text message object
    """
    return {
        "type": "text",
        "text": text
    }

def create_image_message(original_content_url, preview_image_url):
    """
    Create an image message object for LINE
    
    Args:
        original_content_url (str): URL of the original image
        preview_image_url (str): URL of the preview image
    
    Returns:
        dict: Image message object
    """
    return {
        "type": "image",
        "originalContentUrl": original_content_url,
        "previewImageUrl": preview_image_url
    }

def create_video_message(original_content_url, preview_image_url):
    """
    Create a video message object for LINE
    
    Args:
        original_content_url (str): URL of the original video
        preview_image_url (str): URL of the preview image
    
    Returns:
        dict: Video message object
    """
    return {
        "type": "video",
        "originalContentUrl": original_content_url,
        "previewImageUrl": preview_image_url
    }

def create_audio_message(original_content_url, duration):
    """
    Create an audio message object for LINE
    
    Args:
        original_content_url (str): URL of the original audio
        duration (int): Audio duration in milliseconds
    
    Returns:
        dict: Audio message object
    """
    return {
        "type": "audio",
        "originalContentUrl": original_content_url,
        "duration": duration
    }

def create_sticker_message(package_id, sticker_id):
    """
    Create a sticker message object for LINE
    
    Args:
        package_id (str): Sticker package ID
        sticker_id (str): Sticker ID
    
    Returns:
        dict: Sticker message object
    """
    return {
        "type": "sticker",
        "packageId": package_id,
        "stickerId": sticker_id
    }

def parse_line_webhook_event(event_data):
    """
    Parse LINE webhook event data
    
    Args:
        event_data (dict): Webhook event data
    
    Returns:
        dict: Parsed event information
    """
    event_type = event_data.get('type')
    source = event_data.get('source', {})
    
    parsed_event = {
        'event_type': event_type,
        'timestamp': event_data.get('timestamp'),
        'source_type': source.get('type'),
        'source_user_id': source.get('userId'),
        'source_group_id': source.get('groupId'),
        'source_room_id': source.get('roomId'),
        'reply_token': event_data.get('replyToken')
    }
    
    # Parse message events
    if event_type == 'message':
        message = event_data.get('message', {})
        parsed_event.update({
            'message_id': message.get('id'),
            'message_type': message.get('type'),
            'message_text': message.get('text'),
            'message_content_url': message.get('contentProvider', {}).get('originalContentUrl')
        })
    
    # Parse follow/unfollow events
    elif event_type in ['follow', 'unfollow']:
        parsed_event['action'] = event_type
    
    # Parse join/leave events
    elif event_type in ['join', 'leave']:
        parsed_event['action'] = event_type
    
    return parsed_event
