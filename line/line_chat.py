import requests
import json
from datetime import datetime
from django.http import JsonResponse

# LINE Messaging API for chat functionality
LINE_MESSAGING_API_BASE = "https://api.line.me/v2/bot"

def get_line_conversations(access_token, limit=100):
    """
    Get LINE conversations/chats
    Note: LINE doesn't provide a direct API to list all conversations
    This is a placeholder for when such functionality becomes available
    
    Args:
        access_token (str): LINE channel access token
        limit (int): Number of conversations to retrieve
    
    Returns:
        dict: Conversations data
    """
    # LINE doesn't currently provide an API to list conversations
    # This would need to be implemented using webhook events to track conversations
    return {
        'success': False,
        'message': 'LINE does not provide a direct API to list conversations. Use webhook events to track chats.',
        'conversations': []
    }

def get_line_chat_messages(access_token, user_id, limit=100):
    """
    Get messages from a specific LINE chat
    Note: LINE doesn't provide message history API
    Messages need to be stored from webhook events
    
    Args:
        access_token (str): LINE channel access token
        user_id (str): LINE user ID
        limit (int): Number of messages to retrieve
    
    Returns:
        dict: Messages data
    """
    # LINE doesn't provide message history API
    # Messages need to be stored when received via webhooks
    return {
        'success': False,
        'message': 'LINE does not provide message history API. Store messages from webhook events.',
        'messages': []
    }

def send_line_text_message(access_token, to_user_id, message_text):
    """
    Send text message to LINE user
    
    Args:
        access_token (str): LINE channel access token
        to_user_id (str): Target user ID
        message_text (str): Message text to send
    
    Returns:
        dict: API response
    """
    url = f"{LINE_MESSAGING_API_BASE}/message/push"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {access_token}"
    }
    
    payload = {
        "to": to_user_id,
        "messages": [
            {
                "type": "text",
                "text": message_text
            }
        ]
    }
    
    response = requests.post(url, headers=headers, json=payload)
    
    if response.status_code == 200:
        return {"success": True, "data": response.json()}
    else:
        return {"success": False, "error": response.text, "status_code": response.status_code}

def send_line_image_message(access_token, to_user_id, original_content_url, preview_image_url):
    """
    Send image message to LINE user
    
    Args:
        access_token (str): LINE channel access token
        to_user_id (str): Target user ID
        original_content_url (str): URL of the original image
        preview_image_url (str): URL of the preview image
    
    Returns:
        dict: API response
    """
    url = f"{LINE_MESSAGING_API_BASE}/message/push"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {access_token}"
    }
    
    payload = {
        "to": to_user_id,
        "messages": [
            {
                "type": "image",
                "originalContentUrl": original_content_url,
                "previewImageUrl": preview_image_url
            }
        ]
    }
    
    response = requests.post(url, headers=headers, json=payload)
    
    if response.status_code == 200:
        return {"success": True, "data": response.json()}
    else:
        return {"success": False, "error": response.text, "status_code": response.status_code}

def send_line_video_message(access_token, to_user_id, original_content_url, preview_image_url):
    """
    Send video message to LINE user
    
    Args:
        access_token (str): LINE channel access token
        to_user_id (str): Target user ID
        original_content_url (str): URL of the original video
        preview_image_url (str): URL of the preview image
    
    Returns:
        dict: API response
    """
    url = f"{LINE_MESSAGING_API_BASE}/message/push"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {access_token}"
    }
    
    payload = {
        "to": to_user_id,
        "messages": [
            {
                "type": "video",
                "originalContentUrl": original_content_url,
                "previewImageUrl": preview_image_url
            }
        ]
    }
    
    response = requests.post(url, headers=headers, json=payload)
    
    if response.status_code == 200:
        return {"success": True, "data": response.json()}
    else:
        return {"success": False, "error": response.text, "status_code": response.status_code}

def send_line_sticker_message(access_token, to_user_id, package_id, sticker_id):
    """
    Send sticker message to LINE user
    
    Args:
        access_token (str): LINE channel access token
        to_user_id (str): Target user ID
        package_id (str): Sticker package ID
        sticker_id (str): Sticker ID
    
    Returns:
        dict: API response
    """
    url = f"{LINE_MESSAGING_API_BASE}/message/push"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {access_token}"
    }
    
    payload = {
        "to": to_user_id,
        "messages": [
            {
                "type": "sticker",
                "packageId": package_id,
                "stickerId": sticker_id
            }
        ]
    }
    
    response = requests.post(url, headers=headers, json=payload)
    
    if response.status_code == 200:
        return {"success": True, "data": response.json()}
    else:
        return {"success": False, "error": response.text, "status_code": response.status_code}

def get_line_user_profile_info(access_token, user_id):
    """
    Get LINE user profile information
    
    Args:
        access_token (str): LINE channel access token
        user_id (str): LINE user ID
    
    Returns:
        dict: User profile information
    """
    url = f"{LINE_MESSAGING_API_BASE}/profile/{user_id}"
    headers = {
        "Authorization": f"Bearer {access_token}"
    }
    
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:
        profile_data = response.json()
        return {
            "success": True,
            "data": {
                "user_id": profile_data.get("userId"),
                "display_name": profile_data.get("displayName"),
                "picture_url": profile_data.get("pictureUrl"),
                "status_message": profile_data.get("statusMessage")
            }
        }
    else:
        return {"success": False, "error": response.text, "status_code": response.status_code}

def create_line_rich_menu(access_token, rich_menu_data):
    """
    Create LINE rich menu
    
    Args:
        access_token (str): LINE channel access token
        rich_menu_data (dict): Rich menu configuration
    
    Returns:
        dict: API response with rich menu ID
    """
    url = f"{LINE_MESSAGING_API_BASE}/richmenu"
    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {access_token}"
    }
    
    response = requests.post(url, headers=headers, json=rich_menu_data)
    
    if response.status_code == 200:
        return {"success": True, "data": response.json()}
    else:
        return {"success": False, "error": response.text, "status_code": response.status_code}

def set_line_rich_menu_image(access_token, rich_menu_id, image_data):
    """
    Set image for LINE rich menu
    
    Args:
        access_token (str): LINE channel access token
        rich_menu_id (str): Rich menu ID
        image_data (bytes): Image data
    
    Returns:
        dict: API response
    """
    url = f"{LINE_MESSAGING_API_BASE}/richmenu/{rich_menu_id}/content"
    headers = {
        "Content-Type": "image/jpeg",
        "Authorization": f"Bearer {access_token}"
    }
    
    response = requests.post(url, headers=headers, data=image_data)
    
    if response.status_code == 200:
        return {"success": True}
    else:
        return {"success": False, "error": response.text, "status_code": response.status_code}

def link_line_rich_menu_to_user(access_token, user_id, rich_menu_id):
    """
    Link rich menu to specific LINE user
    
    Args:
        access_token (str): LINE channel access token
        user_id (str): LINE user ID
        rich_menu_id (str): Rich menu ID
    
    Returns:
        dict: API response
    """
    url = f"{LINE_MESSAGING_API_BASE}/user/{user_id}/richmenu/{rich_menu_id}"
    headers = {
        "Authorization": f"Bearer {access_token}"
    }
    
    response = requests.post(url, headers=headers)
    
    if response.status_code == 200:
        return {"success": True}
    else:
        return {"success": False, "error": response.text, "status_code": response.status_code}

def get_line_message_content(access_token, message_id):
    """
    Get content of LINE message (for images, videos, audio)
    
    Args:
        access_token (str): LINE channel access token
        message_id (str): Message ID
    
    Returns:
        bytes: Message content data
    """
    url = f"{LINE_MESSAGING_API_BASE}/message/{message_id}/content"
    headers = {
        "Authorization": f"Bearer {access_token}"
    }
    
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:
        return response.content
    else:
        return None

def format_line_chat_message(message_data, user_profile=None):
    """
    Format LINE message data for consistent display
    
    Args:
        message_data (dict): Raw message data from webhook
        user_profile (dict): User profile information
    
    Returns:
        dict: Formatted message data
    """
    formatted_message = {
        'message_id': message_data.get('id'),
        'type': message_data.get('type'),
        'timestamp': message_data.get('timestamp'),
        'user_id': message_data.get('source', {}).get('userId'),
        'user_name': user_profile.get('displayName', 'Unknown User') if user_profile else 'Unknown User',
        'user_avatar': user_profile.get('pictureUrl', '') if user_profile else '',
        'content': {}
    }
    
    # Format content based on message type
    message_type = message_data.get('type')
    if message_type == 'text':
        formatted_message['content'] = {
            'text': message_data.get('text', '')
        }
    elif message_type == 'image':
        formatted_message['content'] = {
            'content_provider': message_data.get('contentProvider', {}),
            'image_set': message_data.get('imageSet', {})
        }
    elif message_type == 'video':
        formatted_message['content'] = {
            'content_provider': message_data.get('contentProvider', {}),
            'duration': message_data.get('duration', 0)
        }
    elif message_type == 'audio':
        formatted_message['content'] = {
            'content_provider': message_data.get('contentProvider', {}),
            'duration': message_data.get('duration', 0)
        }
    elif message_type == 'sticker':
        formatted_message['content'] = {
            'package_id': message_data.get('packageId'),
            'sticker_id': message_data.get('stickerId'),
            'sticker_resource_type': message_data.get('stickerResourceType')
        }
    
    return formatted_message
