import requests
import hashlib
import hmac
import urllib.parse
from urllib.parse import urlencode

# LINE OAuth 2.0 Configuration
# Based on your provided credentials
LINE_CHANNEL_ID = "2007630314"
LINE_CHANNEL_SECRET = "6b81e6bc52b9258d5d3d7f0b35034514"
LINE_REDIRECT_URI = "https://suitable-arguably-bird.ngrok-free.app/api/line-callback/"

# LINE OAuth URLs
LINE_AUTH_URL = "https://manager.line.biz/module/auth/v1/authorize"
LINE_TOKEN_URL = "https://api.line.me/oauth2/v2.1/token"
LINE_PROFILE_URL = "https://api.line.me/v2/profile"
LINE_REVOKE_URL = "https://api.line.me/oauth2/v2.1/revoke"

def generate_line_auth_url(brand_id, scopes=None):
    """
    Generate LINE OAuth 2.0 authorization URL for module channel attachment
    
    Args:
        brand_id (str): Brand ID to use as state parameter
        scopes (list): List of scopes to request (optional)
    
    Returns:
        str: Authorization URL
    """
    if scopes is None:
        # Default scopes for LINE module channel
        scopes = ["message:send", "message:receive", "profile:read"]
    
    # Convert scopes list to URL-encoded string
    scope_string = "%20".join(scopes)  # URL-encoded space
    
    params = {
        "response_type": "code",
        "client_id": LINE_CHANNEL_ID,
        "redirect_uri": LINE_REDIRECT_URI,
        "scope": scope_string,
        "state": brand_id,
        "region": "JP"  # You can change this based on your target region
    }
    
    auth_url = f"{LINE_AUTH_URL}?{urlencode(params)}"
    return auth_url

def exchange_code_for_token(authorization_code):
    """
    Exchange authorization code for access token
    
    Args:
        authorization_code (str): Authorization code from LINE callback
    
    Returns:
        dict: Token response containing access_token, refresh_token, etc.
    """
    headers = {
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    data = {
        "grant_type": "authorization_code",
        "code": authorization_code,
        "redirect_uri": LINE_REDIRECT_URI,
        "client_id": LINE_CHANNEL_ID,
        "client_secret": LINE_CHANNEL_SECRET
    }
    
    response = requests.post(LINE_TOKEN_URL, headers=headers, data=data)
    
    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"Token exchange failed: {response.status_code} - {response.text}")

def get_line_profile(access_token):
    """
    Get LINE user profile information
    
    Args:
        access_token (str): LINE access token
    
    Returns:
        dict: User profile information
    """
    headers = {
        "Authorization": f"Bearer {access_token}"
    }
    
    response = requests.get(LINE_PROFILE_URL, headers=headers)
    
    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"Profile fetch failed: {response.status_code} - {response.text}")

def refresh_line_token(refresh_token):
    """
    Refresh LINE access token using refresh token
    
    Args:
        refresh_token (str): LINE refresh token
    
    Returns:
        dict: New token response
    """
    headers = {
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    data = {
        "grant_type": "refresh_token",
        "refresh_token": refresh_token,
        "client_id": LINE_CHANNEL_ID,
        "client_secret": LINE_CHANNEL_SECRET
    }
    
    response = requests.post(LINE_TOKEN_URL, headers=headers, data=data)
    
    if response.status_code == 200:
        return response.json()
    else:
        raise Exception(f"Token refresh failed: {response.status_code} - {response.text}")

def revoke_line_token(access_token):
    """
    Revoke LINE access token
    
    Args:
        access_token (str): LINE access token to revoke
    
    Returns:
        bool: True if successful
    """
    headers = {
        "Content-Type": "application/x-www-form-urlencoded"
    }
    
    data = {
        "access_token": access_token,
        "client_id": LINE_CHANNEL_ID,
        "client_secret": LINE_CHANNEL_SECRET
    }
    
    response = requests.post(LINE_REVOKE_URL, headers=headers, data=data)
    
    return response.status_code == 200

def validate_line_webhook_signature(body, signature, channel_secret):
    """
    Validate LINE webhook signature for security
    
    Args:
        body (bytes): Request body
        signature (str): X-Line-Signature header value
        channel_secret (str): LINE channel secret
    
    Returns:
        bool: True if signature is valid
    """
    hash_value = hmac.new(
        channel_secret.encode('utf-8'),
        body,
        hashlib.sha256
    ).digest()
    
    expected_signature = 'sha256=' + hash_value.hex()
    
    return hmac.compare_digest(signature, expected_signature)

def attach_module_channel(authorization_code, brand_id):
    """
    Complete LINE module channel attachment process
    
    Args:
        authorization_code (str): Authorization code from LINE
        brand_id (str): Brand ID for the attachment
    
    Returns:
        dict: Complete attachment result with tokens and profile
    """
    try:
        # Step 1: Exchange code for tokens
        token_response = exchange_code_for_token(authorization_code)
        
        # Step 2: Get user profile
        profile_response = get_line_profile(token_response['access_token'])
        
        # Step 3: Return combined result
        return {
            'success': True,
            'access_token': token_response.get('access_token'),
            'refresh_token': token_response.get('refresh_token'),
            'expires_in': token_response.get('expires_in'),
            'token_type': token_response.get('token_type'),
            'scope': token_response.get('scope'),
            'user_id': profile_response.get('userId'),
            'display_name': profile_response.get('displayName'),
            'picture_url': profile_response.get('pictureUrl'),
            'status_message': profile_response.get('statusMessage'),
            'brand_id': brand_id
        }
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'brand_id': brand_id
        }
