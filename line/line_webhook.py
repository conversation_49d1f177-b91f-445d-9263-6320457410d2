import json
import hashlib
import hmac
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status

from .line_auth import LINE_CHANNEL_SECRET, validate_line_webhook_signature
from .line_messaging import (
    send_line_reply_message, 
    create_text_message, 
    parse_line_webhook_event,
    get_line_user_profile
)

@method_decorator(csrf_exempt, name='dispatch')
class LineWebhookView(APIView):
    """
    Handle LINE webhook events
    This endpoint receives webhook events from LINE platform
    """
    
    def post(self, request):
        try:
            # Get request body and signature
            body = request.body
            signature = request.headers.get('X-Line-Signature', '')
            
            if not signature:
                return Response({
                    'status': False,
                    'message': 'Missing X-Line-Signature header'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Validate webhook signature
            if not validate_line_webhook_signature(body, signature, LINE_CHANNEL_SECRET):
                return Response({
                    'status': False,
                    'message': 'Invalid signature'
                }, status=status.HTTP_401_UNAUTHORIZED)
            
            # Parse webhook data
            try:
                webhook_data = json.loads(body.decode('utf-8'))
            except json.JSONDecodeError:
                return Response({
                    'status': False,
                    'message': 'Invalid JSON payload'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Process each event
            events = webhook_data.get('events', [])
            for event in events:
                self.process_line_event(event)
            
            return Response({'status': True}, status=status.HTTP_200_OK)
            
        except Exception as e:
            return Response({
                'status': False,
                'message': f'Webhook processing error: {str(e)}'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def process_line_event(self, event):
        """
        Process individual LINE webhook event
        
        Args:
            event (dict): LINE webhook event data
        """
        try:
            # Parse the event
            parsed_event = parse_line_webhook_event(event)
            event_type = parsed_event['event_type']
            
            # Handle different event types
            if event_type == 'message':
                self.handle_message_event(parsed_event, event)
            elif event_type == 'follow':
                self.handle_follow_event(parsed_event, event)
            elif event_type == 'unfollow':
                self.handle_unfollow_event(parsed_event, event)
            elif event_type == 'join':
                self.handle_join_event(parsed_event, event)
            elif event_type == 'leave':
                self.handle_leave_event(parsed_event, event)
            else:
                print(f"Unhandled event type: {event_type}")
                
        except Exception as e:
            print(f"Error processing LINE event: {str(e)}")
    
    def handle_message_event(self, parsed_event, original_event):
        """
        Handle incoming message events
        
        Args:
            parsed_event (dict): Parsed event data
            original_event (dict): Original LINE event data
        """
        try:
            message_type = parsed_event['message_type']
            reply_token = parsed_event['reply_token']
            user_id = parsed_event['source_user_id']
            
            # Handle text messages
            if message_type == 'text':
                message_text = parsed_event['message_text']
                print(f"Received text message from {user_id}: {message_text}")
                
                # Echo the message back (you can customize this logic)
                reply_messages = [create_text_message(f"You said: {message_text}")]
                
                # Send reply (you'll need to get the access token from your database)
                # For now, we'll just log it
                print(f"Would reply to {user_id} with: {reply_messages}")
                
                # TODO: Implement actual reply logic with stored access token
                # self.send_reply_message(reply_token, reply_messages, user_id)
                
            elif message_type == 'image':
                print(f"Received image message from {user_id}")
                reply_messages = [create_text_message("Thanks for the image!")]
                # TODO: Handle image processing
                
            elif message_type == 'video':
                print(f"Received video message from {user_id}")
                reply_messages = [create_text_message("Thanks for the video!")]
                # TODO: Handle video processing
                
            elif message_type == 'audio':
                print(f"Received audio message from {user_id}")
                reply_messages = [create_text_message("Thanks for the audio!")]
                # TODO: Handle audio processing
                
            else:
                print(f"Unhandled message type: {message_type}")
                
        except Exception as e:
            print(f"Error handling message event: {str(e)}")
    
    def handle_follow_event(self, parsed_event, original_event):
        """
        Handle user follow events
        
        Args:
            parsed_event (dict): Parsed event data
            original_event (dict): Original LINE event data
        """
        try:
            user_id = parsed_event['source_user_id']
            reply_token = parsed_event['reply_token']
            
            print(f"User {user_id} followed the LINE account")
            
            # Send welcome message
            welcome_message = create_text_message("Welcome! Thanks for following us!")
            # TODO: Implement actual reply logic
            print(f"Would send welcome message to {user_id}")
            
        except Exception as e:
            print(f"Error handling follow event: {str(e)}")
    
    def handle_unfollow_event(self, parsed_event, original_event):
        """
        Handle user unfollow events
        
        Args:
            parsed_event (dict): Parsed event data
            original_event (dict): Original LINE event data
        """
        try:
            user_id = parsed_event['source_user_id']
            print(f"User {user_id} unfollowed the LINE account")
            
            # TODO: Update user status in database
            
        except Exception as e:
            print(f"Error handling unfollow event: {str(e)}")
    
    def handle_join_event(self, parsed_event, original_event):
        """
        Handle bot join group/room events
        
        Args:
            parsed_event (dict): Parsed event data
            original_event (dict): Original LINE event data
        """
        try:
            source_type = parsed_event['source_type']
            group_id = parsed_event.get('source_group_id')
            room_id = parsed_event.get('source_room_id')
            
            if source_type == 'group':
                print(f"Bot joined group: {group_id}")
            elif source_type == 'room':
                print(f"Bot joined room: {room_id}")
                
            # Send greeting message
            greeting_message = create_text_message("Hello everyone! Thanks for adding me to the group!")
            # TODO: Implement actual reply logic
            
        except Exception as e:
            print(f"Error handling join event: {str(e)}")
    
    def handle_leave_event(self, parsed_event, original_event):
        """
        Handle bot leave group/room events
        
        Args:
            parsed_event (dict): Parsed event data
            original_event (dict): Original LINE event data
        """
        try:
            source_type = parsed_event['source_type']
            group_id = parsed_event.get('source_group_id')
            room_id = parsed_event.get('source_room_id')
            
            if source_type == 'group':
                print(f"Bot left group: {group_id}")
            elif source_type == 'room':
                print(f"Bot left room: {room_id}")
                
            # TODO: Update group/room status in database
            
        except Exception as e:
            print(f"Error handling leave event: {str(e)}")
    
    def send_reply_message(self, reply_token, messages, user_id):
        """
        Send reply message using stored access token
        
        Args:
            reply_token (str): Reply token from webhook
            messages (list): List of message objects
            user_id (str): LINE user ID
        """
        try:
            # TODO: Get access token from database based on user_id or channel
            # For now, this is a placeholder
            access_token = "YOUR_CHANNEL_ACCESS_TOKEN"
            
            result = send_line_reply_message(access_token, reply_token, messages)
            
            if result['success']:
                print(f"Reply sent successfully to {user_id}")
            else:
                print(f"Failed to send reply to {user_id}: {result['error']}")
                
        except Exception as e:
            print(f"Error sending reply message: {str(e)}")


# Function-based view for webhook (alternative approach)
@csrf_exempt
@require_http_methods(["POST"])
def line_webhook_handler(request):
    """
    Function-based LINE webhook handler
    Alternative to the class-based view above
    """
    try:
        # Get request body and signature
        body = request.body
        signature = request.headers.get('X-Line-Signature', '')
        
        if not signature:
            return JsonResponse({
                'status': False,
                'message': 'Missing X-Line-Signature header'
            }, status=400)
        
        # Validate webhook signature
        if not validate_line_webhook_signature(body, signature, LINE_CHANNEL_SECRET):
            return JsonResponse({
                'status': False,
                'message': 'Invalid signature'
            }, status=401)
        
        # Parse webhook data
        try:
            webhook_data = json.loads(body.decode('utf-8'))
        except json.JSONDecodeError:
            return JsonResponse({
                'status': False,
                'message': 'Invalid JSON payload'
            }, status=400)
        
        # Process events
        events = webhook_data.get('events', [])
        for event in events:
            print(f"Received LINE event: {event}")
            # Add your event processing logic here
        
        return JsonResponse({'status': True}, status=200)
        
    except Exception as e:
        return JsonResponse({
            'status': False,
            'message': f'Webhook processing error: {str(e)}'
        }, status=500)
